ALTER TABLE "escrow" ALTER COLUMN "status" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "transactions" ALTER COLUMN "status" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."status";--> statement-breakpoint
CREATE TYPE "public"."status" AS ENUM('completed', 'accepted', 'disputed', 'cancelled', 'expired', 'declined', 'pending', 'failed');--> statement-breakpoint
ALTER TABLE "escrow" ALTER COLUMN "status" SET DATA TYPE "public"."status" USING "status"::"public"."status";--> statement-breakpoint
ALTER TABLE "transactions" ALTER COLUMN "status" SET DATA TYPE "public"."status" USING "status"::"public"."status";--> statement-breakpoint
ALTER TABLE "transactions" ALTER COLUMN "amount" SET DATA TYPE varchar;--> statement-breakpoint
ALTER TABLE "transactions" DROP COLUMN "attashment";