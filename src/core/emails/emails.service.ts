import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';

@Injectable()
export class EmailsService {
  private readonly logger = new Logger(EmailsService.name);

  constructor(private readonly mailerService: MailerService) {}

  async sendWelcomeEmail(userEmail: string, userName: string): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: userEmail,
        subject: 'Welcome to Hold X!',
        template: './welcome.ejs',
        context: {
          userName,
          platformName: 'Hold EXx',
          loginUrl: 'https://holdx.com/login',
          unsubscribeUrl: 'https://holdx.com/unsubscribe',
          platformDomain: 'holdx.com',
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  async sendVerificationEmail(
    userEmail: string,
    userName: string,
    verificationToken: string,
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: userEmail,
        subject: 'Verify Your Email Address',
        template: './verify-account.ejs',
        context: {
          userName,
          platformName: 'Hold X',
          verificationToken,
          unsubscribeUrl: 'https://holdx.com/unsubscribe',
          platformDomain: 'holdx.com',
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }
  async sendResetPasswordEmail(
    userEmail: string,
    userName: string,
    resetToken: string,
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: userEmail,
        subject: 'Reset Your Password',
        template: './reset-password.ejs',
        context: {
          userName,
          platformName: 'Hold X',
          resetToken,
          unsubscribeUrl: 'https://holdx.com/unsubscribe',
          platformDomain: 'holdx.com',
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }
  async sendCustomEmail(
    userEmail: string,
    userName: string,
    subject: string,
    message: string,
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: userEmail,
        subject: subject,
        template: './custom.ejs',
        context: {
          userName,
          platformName: 'Hold X',
          title: subject,

          message,
          unsubscribeUrl: 'https://holdx.com/unsubscribe',
          platformDomain: 'holdx.com',
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  async sendTransactionCreatedEmail(
    userEmail: string,
    userName: string,
    transactionTitle: string,
    amount: string,
    isInitiator: boolean = true,
  ): Promise<void> {
    try {
      const subject = isInitiator
        ? 'Transaction Created Successfully'
        : 'New Transaction Request Received';

      const message = isInitiator
        ? `Your transaction "${transactionTitle}" has been created successfully for $${amount}. The receiver will be notified and you will receive updates on the transaction status.`
        : `You have received a new transaction request for "${transactionTitle}" with amount $${amount}. Please review and respond accordingly.`;

      await this.sendCustomEmail(userEmail, userName, subject, message);
    } catch (error) {
      this.logger.error(error);
    }
  }

  async sendTransactionStatusUpdateEmail(
    userEmail: string,
    userName: string,
    transactionTitle: string,
    status: string,
  ): Promise<void> {
    try {
      const statusMessages = {
        completed: 'has been completed successfully',
        disputed: 'is under dispute',
        cancelled: 'has been cancelled',
        expired: 'has expired',
        failed: 'has failed',
      };

      const statusMessage =
        statusMessages[status as keyof typeof statusMessages] ||
        `status has been updated to ${status}`;

      const subject = 'Transaction Status Update';
      const message = `Your transaction "${transactionTitle}" ${statusMessage}.`;

      await this.sendCustomEmail(userEmail, userName, subject, message);
    } catch (error) {
      this.logger.error(error);
    }
  }

  async sendTransactionCancelledEmail(
    userEmail: string,
    userName: string,
    transactionTitle: string,
    cancelledBy: string,
  ): Promise<void> {
    try {
      const subject = 'Transaction Cancelled';
      const message = `The transaction "${transactionTitle}" has been cancelled by ${cancelledBy}.`;

      await this.sendCustomEmail(userEmail, userName, subject, message);
    } catch (error) {
      this.logger.error(error);
    }
  }
}
