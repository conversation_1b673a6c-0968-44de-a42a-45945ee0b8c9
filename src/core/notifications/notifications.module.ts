import { Global, Module } from '@nestjs/common';
import { NotificationsService } from './notifications.service';
import { NotificationRepository, UserRepository } from 'src/common/repository';
import { NotificationsController } from './notifications.controller';

@Global()
@Module({
  controllers: [NotificationsController],
  providers: [NotificationsService, NotificationRepository, UserRepository],
  exports: [NotificationsService],
})
export class NotificationsModule {}
