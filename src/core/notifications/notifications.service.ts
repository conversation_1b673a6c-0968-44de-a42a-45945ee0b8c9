import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { NotificationRepository, UserRepository } from 'src/common/repository';
import { NewNotification, Notification } from 'src/common/schemas';
import { PaginationQuery, PaginatedResponse } from 'src/common/interfaces';

@Injectable()
export class NotificationsService {
  constructor(
    private readonly notificationRepository: NotificationRepository,
    private readonly userRepository: UserRepository,
  ) {}

  async addPushToken(userId: number, token: string) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      await this.userRepository.update(userId, { notificationToken: token });
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async create(notificationData: NewNotification): Promise<Notification> {
    try {
      return await this.notificationRepository.create(notificationData);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findById(id: number): Promise<Notification | null> {
    try {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) throw new NotFoundException('Notification not found');
      return notification;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async findByUserId(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Notification> | null> {
    try {
      return await this.notificationRepository.findByUserId(userId, query);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async markAsRead(id: number): Promise<Notification | null> {
    try {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) throw new NotFoundException('Notification not found');
      return await this.notificationRepository.markAsRead(id);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async markAsReadAll(userId: number): Promise<boolean> {
    try {
      return await this.notificationRepository.markAsReadAll(userId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) throw new NotFoundException('Notification not found');
      return await this.notificationRepository.delete(id);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async deleteAll(userId: number): Promise<boolean> {
    try {
      return await this.notificationRepository.deleteAll(userId);
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
