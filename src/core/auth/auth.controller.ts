import { Controller, UseGuards, Body, Post } from '@nestjs/common';
import { AuthService } from './auth.service';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { LoginDto } from './dto/login.dto';
import { SignUpDto } from './dto/signup.dto';
import { EmailDto } from './dto/email.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { TokenDto } from './dto/token.dto';
import { LocalAuthGuard } from 'src/common/guards/local.guard';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @UseGuards(LocalAuthGuard)
  @ApiOperation({ summary: 'Login user' })
  @ApiResponse({ status: 200, description: 'Returns access token' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async login(@Body() dto: LoginDto) {
    return this.authService.validateUser(dto);
  }

  @Post('signup')
  @ApiOperation({ summary: 'Register new user' })
  @ApiResponse({ status: 201, description: 'Returns access token' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async signup(@Body() dto: SignUpDto) {
    return this.authService.signUp(dto);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Request password reset' })
  @ApiResponse({ status: 200, description: 'Password reset email sent' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async forgotPassword(@Body() dto: EmailDto) {
    return this.authService.forgotPassword(dto);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset password with token' })
  @ApiResponse({ status: 200, description: 'Password updated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid token' })
  async resetPassword(@Body() dto: ResetPasswordDto) {
    return this.authService.resetPassword(dto);
  }

  @Post('test-email')
  async testEmail() {
    return this.authService.testEmail();
  }

  @Post('verify')
  @ApiOperation({ summary: 'Verify user account' })
  @ApiResponse({ status: 200, description: 'Account verified successfully' })
  @ApiResponse({ status: 400, description: 'Invalid verification token' })
  async verifyAccount(@Body() dto: TokenDto) {
    return this.authService.verifyUsersAccount(dto);
  }

  @Post('send-verification')
  @ApiOperation({ summary: 'Send verification token' })
  @ApiResponse({ status: 200, description: 'Verification token sent' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async sendVerification(@Body() dto: EmailDto) {
    return this.authService.sendVerificationToken(dto);
  }
}
