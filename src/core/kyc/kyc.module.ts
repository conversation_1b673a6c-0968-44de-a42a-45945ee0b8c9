import { Modu<PERSON> } from '@nestjs/common';
import { KycService } from './kyc.service';
import { KycController } from './kyc.controller';
import {
  FileRepository,
  KycRepository,
  UserRepository,
} from 'src/common/repository';
import { EmailsService } from '../emails/emails.service';

@Module({
  providers: [
    KycService,
    KycRepository,
    EmailsService,
    UserRepository,
    FileRepository,
  ],
  controllers: [KycController],
})
export class KycModule {}
