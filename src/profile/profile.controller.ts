import {
  Body,
  Controller,
  Get,
  Patch,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ProfileService } from './profile.service';
import { User } from 'src/common/decorators/user.decorator';
import { UpdateProfileDto } from './dto/update.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import {
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';

@ApiTags('Profile')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('profile')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Get()
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({ status: 200, description: 'Returns user profile' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async getProfile(@User('id') userId: number) {
    return this.profileService.getProfile(userId);
  }

  @Patch('image')
  @ApiOperation({ summary: 'Update profile image' })
  @ApiResponse({
    status: 200,
    description: 'Profile image updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  @UseInterceptors(FileInterceptor('image'))
  async updateProfileImage(
    @User('id') userId: number,
    @UploadedFile() image: Express.Multer.File,
  ) {
    return this.profileService.updateProfileImage(userId, image);
  }

  @Patch()
  @ApiOperation({ summary: 'Update user profile' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  @UseInterceptors(FileInterceptor('image'))
  async updateProfile(
    @User('id') userId: number,
    @Body() profileData: UpdateProfileDto,
    @UploadedFile() image: Express.Multer.File,
  ) {
    return this.profileService.updateProfile(userId, profileData, image);
  }
}
