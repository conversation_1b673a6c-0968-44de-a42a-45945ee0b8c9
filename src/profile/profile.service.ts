import { UserRepository } from './../common/repository/user.repository';
import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { ProfileRepository } from 'src/common/repository';
import { FilesService } from 'src/core/files/files.service';
import { UpdateProfileDto } from './dto/update.dto';

@Injectable()
export class ProfileService {
  constructor(
    private readonly profileRepository: ProfileRepository,
    private readonly fileService: FilesService,
    private readonly userRepository: UserRepository,
  ) {}

  async getProfile(userId: number) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      const profile =
        await this.profileRepository.findByUserIdWithUserInfo(userId);
      if (!profile) throw new Error('Profile not found');
      return profile;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async updateProfile(
    userId: number,
    profileData: UpdateProfileDto,
    image: Express.Multer.File,
  ) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      const existingProfile = await this.profileRepository.findByUserId(userId);
      if (!existingProfile) throw new Error('Profile not found');

      if (image) {
        const uploadedFile = await this.fileService.uploadFile(image);
        profileData.avatar = uploadedFile.url;
      }

      const updatedProfile = await this.profileRepository.update(
        existingProfile.id,
        profileData,
      );
      return updatedProfile;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async updateProfileImage(userId: number, file: Express.Multer.File) {
    try {
      const user = await this.userRepository.findById(userId);
      if (!user) throw new NotFoundException('User not found');
      const profile = await this.profileRepository.findByUserId(userId);
      if (!profile) throw new Error('Profile not found');

      const uploadedFile = await this.fileService.uploadFile(file);
      const updatedProfile = await this.profileRepository.update(profile.id, {
        avatar: uploadedFile.url,
      });
      return updatedProfile;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
