import { Module, MiddlewareConsumer } from '@nestjs/common';
import { DatabaseModule } from './database/database.module';
import { ConfigModule } from '@nestjs/config';
import { CoreModule } from './core/core.module';
import { ProfileModule } from './profile/profile.module';
import { EscrowModule } from './escrow/escrow.module';
import { DocumentsModule } from './documents/documents.module';
import { RequestMiddleware } from './common/middleware/request.middleware';
import { AccountsModule } from './escrow/accounts/accounts.module';

@Module({
  imports: [
    ConfigModule.forRoot({ envFilePath: '.env', isGlobal: true }),
    DatabaseModule,
    CoreModule,
    ProfileModule,
    EscrowModule,
    DocumentsModule,
    AccountsModule,
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestMiddleware).forRoutes('*');
  }
}
