import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { AccountsService } from './accounts.service';
import { CreateMobileMoneyAccountDto } from './dto/create-momo-account.dto';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { User } from 'src/common/decorators/user.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('Accounts')
@ApiBearerAuth()
@Controller('accounts')
@UseGuards(JwtAuthGuard)
export class AccountsController {
  constructor(private readonly accountsService: AccountsService) {}

  @Post('mtn')
  @ApiOperation({ summary: 'Link MTN Mobile Money account' })
  @ApiResponse({ status: 201, description: 'Account linked successfully' })
  @ApiResponse({ status: 400, description: 'Invalid account details' })
  linkMtn(
    @Body() createAccountDto: CreateMobileMoneyAccountDto,
    @User('id') userId: number,
  ) {
    return this.accountsService.linkMtnAccount(userId, createAccountDto);
  }

  @Post('orange')
  @ApiOperation({ summary: 'Link Orange Money account' })
  @ApiResponse({ status: 201, description: 'Account linked successfully' })
  @ApiResponse({ status: 400, description: 'Invalid account details' })
  linkOrange(
    @Body() createAccountDto: CreateMobileMoneyAccountDto,
    @User('id') userId: number,
  ) {
    return this.accountsService.linkOrangeAccount(userId, createAccountDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all linked accounts' })
  @ApiResponse({ status: 200, description: 'Returns list of linked accounts' })
  findAll(@User('id') userId: number) {
    return this.accountsService.findAll(userId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get specific account details' })
  @ApiResponse({ status: 200, description: 'Returns account details' })
  @ApiResponse({ status: 404, description: 'Account not found' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.accountsService.findOne(id);
  }

  @Post(':id/default')
  @ApiOperation({ summary: 'Set account as default' })
  @ApiResponse({ status: 200, description: 'Account set as default' })
  @ApiResponse({ status: 404, description: 'Account not found' })
  setAsDefault(@Param('id', ParseIntPipe) id: number) {
    return this.accountsService.setAsDefault(id);
  }

  @Post(':id/activate')
  @ApiOperation({ summary: 'Activate account' })
  @ApiResponse({ status: 200, description: 'Account activated successfully' })
  @ApiResponse({ status: 404, description: 'Account not found' })
  activatePaymentMethod(
    @Param('id', ParseIntPipe) id: number,
    @User('id') userId: number,
  ) {
    return this.accountsService.activatePaymentMethod(id, userId);
  }

  @Post(':id/deactivate')
  @ApiOperation({ summary: 'Deactivate account' })
  @ApiResponse({ status: 200, description: 'Account deactivated successfully' })
  @ApiResponse({ status: 404, description: 'Account not found' })
  deactivatePaymentMethod(
    @Param('id', ParseIntPipe) id: number,
    @User('id') userId: number,
  ) {
    return this.accountsService.deactivatePaymentMethod(id, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Remove linked account' })
  @ApiResponse({ status: 200, description: 'Account removed successfully' })
  @ApiResponse({ status: 404, description: 'Account not found' })
  remove(@Param('id', ParseIntPipe) id: number, @User('id') userId: number) {
    return this.accountsService.remove(id, userId);
  }
}
