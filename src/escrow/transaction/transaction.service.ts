import {
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import {
  TransactionRepository,
  UserRepository,
  PaymentMethodRepository,
} from 'src/common/repository/';
import { CreateTransactionDto } from './dto/create.dto';
import { Transaction, NewTransaction, User } from 'src/common/schemas';
import { EmailsService } from 'src/core/emails/emails.service';
import { NotificationsService } from 'src/core/notifications/notifications.service';
import {
  PaginatedResponse,
  TransactionStatus,
  TransactionFilter,
} from 'src/common/interfaces';
import {
  TransactionStatsDto,
  UserTransactionStatsDto,
  TransactionStatsResponseDto,
  TransactionStatsByPeriodDto,
  TransactionStatsByCategoryDto,
} from './dto/transaction-stats.dto';
import {
  DashboardStatsDto,
  UserDashboardStatsDto,
} from './dto/dashboard-stats.dto';
import { FilesService } from 'src/core/files/files.service';
import { FileRepository } from 'src/common/repository';
import { DeclineTransactionDto } from './dto/decline.dto';
import { AcceptTransactionDto } from './dto/accept.dto';

@Injectable()
export class TransactionService {
  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly userRepository: UserRepository,
    private readonly paymentMethodRepository: PaymentMethodRepository,
    private readonly emailsService: EmailsService,
    private readonly notificationsService: NotificationsService,
    private readonly fileService: FilesService,
    private readonly fileRepository: FileRepository,
  ) {}

  async createTransaction(
    userId: number,
    createTransactionDto: CreateTransactionDto,
    attachments: Express.Multer.File[],
  ): Promise<Transaction> {
    try {
      const initiator = await this.userRepository.findById(userId);
      if (!initiator) throw new NotFoundException('Initiator user not found');

      const receiver = await this.userRepository.findById(
        createTransactionDto.sellerId,
      );
      if (!receiver) throw new NotFoundException('Receiver user not found');

      // Validate payment method exists and belongs to user
      const paymentMethod = await this.paymentMethodRepository.findById(
        createTransactionDto.paymentMethodId,
      );
      if (!paymentMethod)
        throw new NotFoundException('Payment method not found');

      if (paymentMethod.userId !== userId)
        throw new BadRequestException('Payment method does not belong to user');

      const transactionData: NewTransaction = {
        title: createTransactionDto.title,
        description: createTransactionDto.description,
        aggrement: createTransactionDto.aggrement,
        category: createTransactionDto.category,
        deadLine: new Date(createTransactionDto.deadLine),
        initiatedBy: userId,
        receivedBy: createTransactionDto.sellerId,
        paymentMethodId: createTransactionDto.paymentMethodId,
        amount: createTransactionDto.amount,
        status: 'pending',
      };

      const transaction =
        await this.transactionRepository.create(transactionData);

      if (attachments && attachments.length > 0) {
        const uploadedFiles =
          await this.fileService.uploadMultipleFiles(attachments);

        const filePromises = uploadedFiles.map((file) =>
          this.fileRepository.create({
            name: file.original_filename,
            url: file.url,
            transactionId: transaction.id,
          }),
        );

        await Promise.all(filePromises);
      }

      // Send notifications and emails
      await this.sendTransactionNotifications(transaction, initiator, receiver);

      return transaction;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to create transaction',
        error as Error,
      );
    }
  }

  private async sendTransactionNotifications(
    transaction: Transaction,
    initiator: User,
    receiver: User,
  ): Promise<void> {
    try {
      // Send email to receiver
      await this.emailsService.sendTransactionCreatedEmail(
        receiver.email,
        receiver.name,
        transaction.title,
        transaction.amount,
        false, // not initiator
      );

      // Send email to initiator
      await this.emailsService.sendTransactionCreatedEmail(
        initiator.email,
        initiator.name,
        transaction.title,
        transaction.amount,
        true, // is initiator
      );

      // Create notification for receiver
      await this.notificationsService.create({
        title: 'New Transaction Request',
        message: `${initiator.name} sent you a transaction request for "${transaction.title}"`,
        userId: receiver.id,
        type: 'personal',
        category: 'transaction',
        priority: 'high',
        metadata: {
          transactionId: transaction.id,
          initiatorId: initiator.id,
          amount: transaction.amount,
        },
      });

      // Create notification for initiator
      await this.notificationsService.create({
        title: 'Transaction Created',
        message: `Your transaction "${transaction.title}" has been created successfully`,
        userId: initiator.id,
        type: 'personal',
        category: 'transaction',
        priority: 'normal',
        metadata: {
          transactionId: transaction.id,
          receiverId: receiver.id,
          amount: transaction.amount,
        },
      });
    } catch (error) {
      // Log error but don't fail the transaction creation
      console.error('Failed to send notifications:', error);
    }
  }

  async getAllTransactions(
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      return await this.transactionRepository.findAll(query);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions',
        error as Error,
      );
    }
  }

  async declineTransaction(
    userId: number,
    declineTransactionDto: DeclineTransactionDto,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(
        declineTransactionDto.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (transaction.receivedBy !== userId)
        throw new BadRequestException(
          'Only the transaction initiator can decline the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be declined',
        );

      const updatedTransaction = await this.transactionRepository.update(
        transaction.id,
        { status: 'declined' },
      );

      // await this.sendStatusUpdateNotifications(
      //   updatedTransaction,
      //   'declined',
      //   userId,
      // );

      return updatedTransaction;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to decline transaction',
        error as Error,
      );
    }
  }

  async getTransactionById(id: number): Promise<Transaction> {
    try {
      const transaction = await this.transactionRepository.findById(id);
      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }
      return transaction;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to fetch transaction',
        error as Error,
      );
    }
  }

  async getUserTransactions(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction> | null> {
    try {
      return await this.transactionRepository.findByUserId(userId, query);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch user transactions',
        error as Error,
      );
    }
  }

  async getReceivedTransactions(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction> | null> {
    try {
      return await this.transactionRepository.findByReceivedBy(userId, query);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch received transactions',
        error as Error,
      );
    }
  }

  async updateTransactionStatus(
    id: number,
    status: TransactionStatus,
    userId?: number,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(id);
      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }

      const updatedTransaction = await this.transactionRepository.update(id, {
        status: status,
      });

      if (!updatedTransaction) {
        throw new InternalServerErrorException('Failed to update transaction');
      }

      // Send notifications for status updates
      await this.sendStatusUpdateNotifications(
        updatedTransaction,
        status,
        userId,
      );

      return updatedTransaction;
    } catch (error) {
      new InternalServerErrorException(
        'Failed to update transaction status',
        error as Error,
      );
    }
  }

  private async sendStatusUpdateNotifications(
    transaction: Transaction,
    status: string,
    updatedBy?: number,
  ): Promise<void> {
    try {
      const initiator = await this.userRepository.findById(
        transaction.initiatedBy!,
      );
      const receiver = await this.userRepository.findById(
        transaction.receivedBy!,
      );

      if (!initiator || !receiver) return;

      const statusMessages = {
        completed: 'has been completed successfully',
        disputed: 'is under dispute',
        cancelled: 'has been cancelled',
        expired: 'has expired',
        failed: 'has failed',
      };

      const message =
        statusMessages[status as keyof typeof statusMessages] ||
        `status has been updated to ${status}`;

      // Send emails to both parties
      await this.emailsService.sendTransactionStatusUpdateEmail(
        initiator.email,
        initiator.name,
        transaction.title,
        status,
      );

      await this.emailsService.sendTransactionStatusUpdateEmail(
        receiver.email,
        receiver.name,
        transaction.title,
        status,
      );

      // Create notifications for both parties
      await this.notificationsService.create({
        title: 'Transaction Status Update',
        message: `Transaction "${transaction.title}" ${message}`,
        userId: initiator.id,
        type: 'personal',
        category: 'transaction',
        priority: status === 'disputed' ? 'high' : 'normal',
        metadata: {
          transactionId: transaction.id,
          status,
          updatedBy,
        },
      });

      await this.notificationsService.create({
        title: 'Transaction Status Update',
        message: `Transaction "${transaction.title}" ${message}`,
        userId: receiver.id,
        type: 'personal',
        category: 'transaction',
        priority: status === 'disputed' ? 'high' : 'normal',
        metadata: {
          transactionId: transaction.id,
          status,
          updatedBy,
        },
      });
    } catch (error) {
      console.error('Failed to send status update notifications:', error);
    }
  }

  async deleteTransaction(id: number, userId: number): Promise<boolean> {
    try {
      const transaction = await this.transactionRepository.findById(id);
      if (!transaction) {
        throw new NotFoundException('Transaction not found');
      }

      if (transaction.initiatedBy !== userId)
        throw new BadRequestException(
          'Only the transaction initiator can delete the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be deleted',
        );

      const deleted = await this.transactionRepository.delete(id);

      if (deleted) {
        const receiver = await this.userRepository.findById(
          transaction.receivedBy!,
        );
        const initiator = await this.userRepository.findById(
          transaction.initiatedBy,
        );

        if (receiver && initiator) {
          await this.emailsService.sendTransactionCancelledEmail(
            receiver.email,
            receiver.name,
            transaction.title,
            initiator.name,
          );

          await this.notificationsService.create({
            title: 'Transaction Cancelled',
            message: `Transaction "${transaction.title}" has been cancelled by ${initiator.name}`,
            userId: receiver.id,
            type: 'personal',
            category: 'transaction',
            priority: 'normal',
            metadata: {
              transactionId: transaction.id,
              cancelledBy: userId,
            },
          });
        }
      }

      return deleted;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to delete transaction',
        error as Error,
      );
    }
  }

  async acceptTransaction(
    userId: number,
    acceptTransactionDto: AcceptTransactionDto,
  ) {
    try {
      const transaction = await this.transactionRepository.findById(
        acceptTransactionDto.transactionId,
      );
      if (!transaction) throw new NotFoundException('Transaction not found');

      if (transaction.receivedBy !== userId)
        throw new BadRequestException(
          'Only the transaction receiver can accept the transaction',
        );

      if (transaction.status !== 'pending')
        throw new BadRequestException(
          'Only transactions with "pending" status can be accepted',
        );

      const updatedTransaction = await this.transactionRepository.update(
        transaction.id,
        { status: 'accepted' },
      );

      // await this.sendStatusUpdateNotifications(
      //   updatedTransaction,
      //   'completed',
      //   userId,
      // );

      return updatedTransaction;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to accept transaction',
        error as Error,
      );
    }
  }

  async getTransactionsByStatus(
    status: TransactionStatus,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, status };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by status',
        error as Error,
      );
    }
  }

  async getTransactionsByDateRange(
    startDate: string,
    endDate: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = {
        ...query,
        createdFrom: startDate,
        createdTo: endDate,
      };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by date range',
        error as Error,
      );
    }
  }

  async getTransactionsByAmountRange(
    minAmount: number,
    maxAmount: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = {
        ...query,
        minAmount,
        maxAmount,
      };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by amount range',
        error as Error,
      );
    }
  }

  async searchTransactions(
    searchTerm: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, search: searchTerm };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to search transactions',
        error as Error,
      );
    }
  }

  async getTransactionsByCategory(
    category: string,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<Transaction>> {
    try {
      const filterQuery = { ...query, category };
      return await this.transactionRepository.findAll(filterQuery);
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transactions by category',
        error as Error,
      );
    }
  }

  async getGeneralStatistics(): Promise<TransactionStatsResponseDto> {
    try {
      const stats = await this.transactionRepository.getGeneralStats();

      const general: TransactionStatsDto = {
        totalTransactions: stats.general.totalTransactions || 0,
        completedTransactions: this.getStatusCount(stats.byStatus, 'completed'),
        pendingTransactions: this.getStatusCount(stats.byStatus, 'initiated'),
        disputedTransactions: this.getStatusCount(stats.byStatus, 'disputed'),
        cancelledTransactions: this.getStatusCount(stats.byStatus, 'cancelled'),
        failedTransactions: this.getStatusCount(stats.byStatus, 'failed'),
        expiredTransactions: this.getStatusCount(stats.byStatus, 'expired'),
        totalVolume: Number(stats.general.totalVolume) || 0,
        averageAmount: Number(stats.general.averageAmount) || 0,
        maxAmount: Number(stats.general.maxAmount) || 0,
        minAmount: Number(stats.general.minAmount) || 0,
        successRate: this.calculateSuccessRate(stats.byStatus),
      };

      const byStatus: TransactionStatsByCategoryDto[] = stats.byStatus.map(
        (item: any) => ({
          category: item.status,
          count: item.count,
          volume: Number(item.volume) || 0,
          percentage: (item.count / general.totalTransactions) * 100,
        }),
      );

      const byCategory: TransactionStatsByCategoryDto[] = stats.byCategory.map(
        (item: any) => ({
          category: item.category,
          count: item.count,
          volume: Number(item.volume) || 0,
          percentage: (item.count / general.totalTransactions) * 100,
        }),
      );

      return {
        general,
        byStatus,
        byCategory,
        byPeriod: [], // Will be populated by separate method
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch general statistics',
        error as Error,
      );
    }
  }

  async getUserStatistics(userId: number): Promise<UserTransactionStatsDto> {
    try {
      const stats = await this.transactionRepository.getUserStats(userId);

      const totalTransactions =
        (stats.initiated.count || 0) + (stats.received.count || 0);
      const totalVolume =
        Number(stats.initiated.totalAmount || 0) +
        Number(stats.received.totalAmount || 0);

      return {
        userId,
        totalTransactions,
        completedTransactions: this.getStatusCount(stats.byStatus, 'completed'),
        pendingTransactions: this.getStatusCount(stats.byStatus, 'initiated'),
        disputedTransactions: this.getStatusCount(stats.byStatus, 'disputed'),
        cancelledTransactions: this.getStatusCount(stats.byStatus, 'cancelled'),
        failedTransactions: this.getStatusCount(stats.byStatus, 'failed'),
        expiredTransactions: this.getStatusCount(stats.byStatus, 'expired'),
        totalVolume,
        averageAmount:
          totalTransactions > 0 ? totalVolume / totalTransactions : 0,
        maxAmount: Math.max(
          Number(stats.initiated.maxAmount || 0),
          Number(stats.received.maxAmount || 0),
        ),
        minAmount: Math.min(
          Number(stats.initiated.minAmount || 0),
          Number(stats.received.minAmount || 0),
        ),
        successRate: this.calculateSuccessRate(stats.byStatus),
        initiatedTransactions: stats.initiated.count || 0,
        receivedTransactions: stats.received.count || 0,
        totalSent: Number(stats.initiated.totalAmount || 0),
        totalReceived: Number(stats.received.totalAmount || 0),
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch user statistics',
        error as Error,
      );
    }
  }

  async getStatsByDateRange(
    startDate: string,
    endDate: string,
    groupBy: 'day' | 'week' | 'month' = 'day',
  ): Promise<TransactionStatsByPeriodDto[]> {
    try {
      const stats = await this.transactionRepository.getStatsByDateRange(
        new Date(startDate),
        new Date(endDate),
        groupBy,
      );

      return stats.map((item: any) => ({
        period: item.date.toISOString().split('T')[0], // Format as YYYY-MM-DD
        count: item.count,
        volume: Number(item.volume) || 0,
        averageAmount: Number(item.averageAmount) || 0,
      }));
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch statistics by date range',
        error as Error,
      );
    }
  }

  async getTopUsers(limit: number = 10): Promise<any[]> {
    try {
      const topUsers = await this.transactionRepository.getTopUsers(limit);

      // Fetch user details for each top user
      const usersWithDetails = await Promise.all(
        topUsers.map(async (userStat: any) => {
          const user = await this.userRepository.findById(userStat.userId);
          return {
            ...userStat,
            userName: user?.name || 'Unknown User',
            userEmail: user?.email || 'Unknown Email',
            totalVolume: Number(userStat.totalVolume) || 0,
            averageAmount: Number(userStat.averageAmount) || 0,
          };
        }),
      );

      return usersWithDetails;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch top users',
        error as Error,
      );
    }
  }

  async getTransactionTrends(
    days: number = 30,
  ): Promise<TransactionStatsByPeriodDto[]> {
    try {
      const trends =
        await this.transactionRepository.getTransactionTrends(days);

      return trends.map((item: any) => ({
        period: item.date.toISOString().split('T')[0],
        count: item.count,
        volume: Number(item.volume) || 0,
        averageAmount: item.count > 0 ? Number(item.volume) / item.count : 0,
      }));
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch transaction trends',
        error as Error,
      );
    }
  }

  private getStatusCount(statusStats: any[], status: string): number {
    const statusItem = statusStats.find((item: any) => item.status === status);
    return statusItem ? statusItem.count : 0;
  }

  private calculateSuccessRate(statusStats: any[]): number {
    const totalTransactions = statusStats.reduce(
      (sum: number, item: any) => sum + item.count,
      0,
    );
    const completedTransactions = this.getStatusCount(statusStats, 'completed');

    return totalTransactions > 0
      ? (completedTransactions / totalTransactions) * 100
      : 0;
  }

  async getDashboardStatistics(): Promise<DashboardStatsDto> {
    try {
      const [generalStats, trends, topUsers] = await Promise.all([
        this.getGeneralStatistics(),
        this.getTransactionTrends(30),
        this.getTopUsers(5),
      ]);

      // Calculate growth metrics (comparing last 30 days vs previous 30 days)
      const currentPeriodStart = new Date();
      currentPeriodStart.setDate(currentPeriodStart.getDate() - 30);
      const previousPeriodStart = new Date();
      previousPeriodStart.setDate(previousPeriodStart.getDate() - 60);

      const [currentPeriodStats, previousPeriodStats] = await Promise.all([
        this.transactionRepository.getStatsByDateRange(
          currentPeriodStart,
          new Date(),
        ),
        this.transactionRepository.getStatsByDateRange(
          previousPeriodStart,
          currentPeriodStart,
        ),
      ]);

      const currentTotal = currentPeriodStats.reduce(
        (sum: number, item: any) => sum + item.count,
        0,
      );
      const previousTotal = previousPeriodStats.reduce(
        (sum: number, item: any) => sum + item.count,
        0,
      );
      const currentVolume = currentPeriodStats.reduce(
        (sum: number, item: any) => sum + Number(item.volume || 0),
        0,
      );
      const previousVolume = previousPeriodStats.reduce(
        (sum: number, item: any) => sum + Number(item.volume || 0),
        0,
      );

      const transactionGrowth =
        previousTotal > 0
          ? ((currentTotal - previousTotal) / previousTotal) * 100
          : 0;
      const volumeGrowth =
        previousVolume > 0
          ? ((currentVolume - previousVolume) / previousVolume) * 100
          : 0;

      return {
        general: generalStats.general,
        recentTrends: trends,
        byStatus: generalStats.byStatus,
        byCategory: generalStats.byCategory,
        topUsers,
        growth: {
          transactionGrowth,
          volumeGrowth,
          userGrowth: 0, // Would need user activity tracking
        },
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch dashboard statistics',
        error as Error,
      );
    }
  }

  async getUserDashboardStatistics(
    userId: number,
  ): Promise<UserDashboardStatsDto> {
    try {
      const [userStats, userTrends, recentTransactions, allUsers] =
        await Promise.all([
          this.getUserStatistics(userId),
          this.getUserTransactionTrends(userId, 30),
          this.getRecentUserTransactions(userId, 5),
          this.getTopUsers(1000), // Get all users for ranking
        ]);

      // Calculate user ranking
      const userRanking = this.calculateUserRanking(userId, allUsers);

      // Calculate recent activity
      const currentMonth = new Date();
      currentMonth.setDate(1); // First day of current month
      const monthlyTransactions = await this.transactionRepository.findByUserId(
        userId,
        {
          page: 1,
          limit: 1000,
          createdFrom: currentMonth.toISOString(),
        },
      );

      const completedThisMonth =
        monthlyTransactions?.data?.filter(
          (t: any) => t.transaction.status === 'completed',
        ).length || 0;

      return {
        userStats,
        userTrends,
        recentActivity: {
          lastTransaction: recentTransactions[0] || null,
          pendingCount: userStats.pendingTransactions,
          completedThisMonth,
          totalThisMonth: monthlyTransactions?.data?.length || 0,
        },
        ranking: userRanking,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to fetch user dashboard statistics',
        error as Error,
      );
    }
  }

  private async getUserTransactionTrends(
    userId: number,
    days: number,
  ): Promise<TransactionStatsByPeriodDto[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const trends = await this.transactionRepository.getStatsByDateRange(
      startDate,
      new Date(),
    );

    // Filter for user's transactions (this is simplified - in reality you'd need a more complex query)
    return trends.map((item: any) => ({
      period: item.date.toISOString().split('T')[0],
      count: item.count, // This would need to be filtered by user
      volume: Number(item.volume) || 0,
      averageAmount: Number(item.averageAmount) || 0,
    }));
  }

  private async getRecentUserTransactions(
    userId: number,
    limit: number,
  ): Promise<any[]> {
    const result = await this.transactionRepository.findByUserId(userId, {
      page: 1,
      limit,
    });
    return result?.data || [];
  }

  private calculateUserRanking(userId: number, allUsers: any[]): any {
    const userIndex = allUsers.findIndex((user: any) => user.userId === userId);

    return {
      volumeRank: userIndex >= 0 ? userIndex + 1 : allUsers.length + 1,
      transactionCountRank:
        userIndex >= 0 ? userIndex + 1 : allUsers.length + 1,
      totalUsers: allUsers.length,
    };
  }
}
