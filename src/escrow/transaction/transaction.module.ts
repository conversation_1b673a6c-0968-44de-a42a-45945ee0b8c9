import { Modu<PERSON> } from '@nestjs/common';
import { TransactionController } from './transaction.controller';
import { TransactionService } from './transaction.service';
import {
  TransactionRepository,
  UserRepository,
  PaymentMethodRepository,
} from 'src/common/repository';
import { EmailsService } from 'src/core/emails/emails.service';
import { NotificationsService } from 'src/core/notifications/notifications.service';
import { NotificationRepository } from 'src/common/repository';
import { FilesService } from 'src/core/files/files.service';
import { FileRepository } from 'src/common/repository';

@Module({
  controllers: [TransactionController],
  providers: [
    TransactionService,
    TransactionRepository,
    UserRepository,
    PaymentMethodRepository,
    EmailsService,
    NotificationsService,
    FilesService,
    NotificationRepository,
    FileRepository,
  ],
  exports: [TransactionService],
})
export class TransactionModule {}
