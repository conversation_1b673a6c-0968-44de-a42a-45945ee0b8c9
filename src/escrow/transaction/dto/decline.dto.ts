import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class DeclineTransactionDto {
  @ApiProperty({
    example: 'Reason for declining the transaction',
    description: 'Reason for declining the transaction',
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiProperty({
    example: 1,
    description: 'Transaction ID',
  })
  @IsNumber()
  @IsNotEmpty()
  transactionId: number;
}
