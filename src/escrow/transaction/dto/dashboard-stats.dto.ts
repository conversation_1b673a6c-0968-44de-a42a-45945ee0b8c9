import { ApiProperty } from '@nestjs/swagger';
import {
  TransactionStatsDto,
  UserTransactionStatsDto,
  TransactionStatsByPeriodDto,
  TransactionStatsByCategoryDto,
} from './transaction-stats.dto';

export class DashboardStatsDto {
  @ApiProperty({ description: 'General transaction statistics' })
  general: TransactionStatsDto;

  @ApiProperty({ 
    description: 'Recent transaction trends (last 30 days)',
    type: [TransactionStatsByPeriodDto]
  })
  recentTrends: TransactionStatsByPeriodDto[];

  @ApiProperty({ 
    description: 'Statistics by status',
    type: [TransactionStatsByCategoryDto]
  })
  byStatus: TransactionStatsByCategoryDto[];

  @ApiProperty({ 
    description: 'Statistics by category',
    type: [TransactionStatsByCategoryDto]
  })
  byCategory: TransactionStatsByCategoryDto[];

  @ApiProperty({ description: 'Top users by transaction volume' })
  topUsers: any[];

  @ApiProperty({ description: 'Growth metrics' })
  growth: {
    transactionGrowth: number; // Percentage growth in transactions
    volumeGrowth: number; // Percentage growth in volume
    userGrowth: number; // Percentage growth in active users
  };
}

export class UserDashboardStatsDto {
  @ApiProperty({ description: 'User-specific transaction statistics' })
  userStats: UserTransactionStatsDto;

  @ApiProperty({ 
    description: 'User transaction trends (last 30 days)',
    type: [TransactionStatsByPeriodDto]
  })
  userTrends: TransactionStatsByPeriodDto[];

  @ApiProperty({ description: 'Recent transactions summary' })
  recentActivity: {
    lastTransaction: any;
    pendingCount: number;
    completedThisMonth: number;
    totalThisMonth: number;
  };

  @ApiProperty({ description: 'User ranking' })
  ranking: {
    volumeRank: number;
    transactionCountRank: number;
    totalUsers: number;
  };
}
