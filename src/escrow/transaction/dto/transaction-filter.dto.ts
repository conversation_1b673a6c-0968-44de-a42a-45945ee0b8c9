import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsNumber, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { type TransactionStatus } from 'src/common/interfaces';

export class TransactionFilterDto {
  @ApiPropertyOptional({
    description: 'Filter by transaction status',
    enum: [
      'completed',
      'disputed',
      'cancelled',
      'expired',
      'initiated',
      'failed',
    ],
  })
  @IsOptional()
  @IsEnum([
    'completed',
    'disputed',
    'cancelled',
    'expired',
    'initiated',
    'failed',
  ])
  status?: TransactionStatus;

  @ApiPropertyOptional({
    description: 'Filter by transaction category/type',
  })
  @ApiPropertyOptional({
    description: 'Search in title and description',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Sort by field',
    enum: ['createdAt', 'updatedAt', 'amount', 'deadLine', 'title'],
  })
  @IsOptional()
  @IsEnum(['createdAt', 'updatedAt', 'amount', 'deadLine', 'title'])
  sortBy?: 'createdAt' | 'updatedAt' | 'amount' | 'deadLine' | 'title';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: ['asc', 'desc'],
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';

  @ApiPropertyOptional({
    description: 'Page number',
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number;

  @ApiPropertyOptional({
    description: 'Items per page',
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number;
}
