import { ApiProperty } from '@nestjs/swagger';

export class TransactionStatsDto {
  @ApiProperty({ description: 'Total number of transactions' })
  totalTransactions: number;

  @ApiProperty({ description: 'Number of completed transactions' })
  completedTransactions: number;

  @ApiProperty({ description: 'Number of pending/initiated transactions' })
  pendingTransactions: number;

  @ApiProperty({ description: 'Number of disputed transactions' })
  disputedTransactions: number;

  @ApiProperty({ description: 'Number of cancelled transactions' })
  cancelledTransactions: number;

  @ApiProperty({ description: 'Number of failed transactions' })
  failedTransactions: number;

  @ApiProperty({ description: 'Number of expired transactions' })
  expiredTransactions: number;

  @ApiProperty({ description: 'Total transaction volume (amount)' })
  totalVolume: number;

  @ApiProperty({ description: 'Average transaction amount' })
  averageAmount: number;

  @ApiProperty({ description: 'Highest transaction amount' })
  maxAmount: number;

  @ApiProperty({ description: 'Lowest transaction amount' })
  minAmount: number;

  @ApiProperty({ description: 'Success rate percentage' })
  successRate: number;
}

export class UserTransactionStatsDto extends TransactionStatsDto {
  @ApiProperty({ description: 'User ID' })
  userId: number;

  @ApiProperty({ description: 'Number of transactions initiated by user' })
  initiatedTransactions: number;

  @ApiProperty({ description: 'Number of transactions received by user' })
  receivedTransactions: number;

  @ApiProperty({ description: 'Total amount sent by user' })
  totalSent: number;

  @ApiProperty({ description: 'Total amount received by user' })
  totalReceived: number;
}

export class TransactionStatsByPeriodDto {
  @ApiProperty({ description: 'Period (e.g., "2024-01", "2024-01-15")' })
  period: string;

  @ApiProperty({ description: 'Number of transactions in period' })
  count: number;

  @ApiProperty({ description: 'Total volume in period' })
  volume: number;

  @ApiProperty({ description: 'Average amount in period' })
  averageAmount: number;
}

export class TransactionStatsByCategoryDto {
  @ApiProperty({ description: 'Transaction category' })
  category: string;

  @ApiProperty({ description: 'Number of transactions in category' })
  count: number;

  @ApiProperty({ description: 'Total volume in category' })
  volume: number;

  @ApiProperty({ description: 'Percentage of total transactions' })
  percentage: number;
}

export class TransactionStatsResponseDto {
  @ApiProperty({ description: 'General transaction statistics' })
  general: TransactionStatsDto;

  @ApiProperty({ 
    description: 'Statistics by status',
    type: [TransactionStatsByCategoryDto]
  })
  byStatus: TransactionStatsByCategoryDto[];

  @ApiProperty({ 
    description: 'Statistics by category',
    type: [TransactionStatsByCategoryDto]
  })
  byCategory: TransactionStatsByCategoryDto[];

  @ApiProperty({ 
    description: 'Statistics by time period',
    type: [TransactionStatsByPeriodDto]
  })
  byPeriod: TransactionStatsByPeriodDto[];
}
