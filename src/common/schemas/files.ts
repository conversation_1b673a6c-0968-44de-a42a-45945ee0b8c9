import { varchar } from 'drizzle-orm/pg-core';
import { integer } from 'drizzle-orm/pg-core';
import { serial } from 'drizzle-orm/pg-core';
import { pgTable } from 'drizzle-orm/pg-core';
import { transactions } from './transaction';
import { relations } from 'drizzle-orm';
import { text } from 'drizzle-orm/pg-core';
import { kyc } from './kyc';

export const files = pgTable('files', {
  id: serial('id').primaryKey(),
  name: text('name'),
  url: varchar('url', { length: 255 }),
  kycId: integer('kyc_id').references(() => kyc.id),
  transactionId: integer('transaction_id').references(() => transactions.id),
});

export type File = typeof files.$inferSelect;
export type NewFile = typeof files.$inferInsert;

export const fileRelations = relations(files, ({ one }) => ({
  transaction: one(transactions, {
    fields: [files.transactionId],
    references: [transactions.id],
  }),
  kyc: one(kyc, {
    fields: [files.kycId],
    references: [kyc.id],
  }),
}));
