import { pgTable, serial, varchar, timestamp } from 'drizzle-orm/pg-core';
import { pgEnum } from 'drizzle-orm/pg-core';
import { users } from './users';
import { integer } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { files } from './files';

export const kycStatusEnum = pgEnum('kyc_status', [
  'pending',
  'approved',
  'rejected',
]);
export const kyc = pgTable('kyc', {
  id: serial('id').primaryKey(),
  userId: integer('user_id').references(() => users.id),
  country: varchar('country', { length: 255 }),
  dob: timestamp('dob'),
  phoneNumber: varchar('phone_number', { length: 255 }),
  address: varchar('address', { length: 255 }),
  status: kycStatusEnum('status').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export type Kyc = typeof kyc.$inferSelect;
export type NewKyc = typeof kyc.$inferInsert;

export const kycRelation = relations(kyc, ({ one, many }) => ({
  user: one(users, {
    fields: [kyc.userId],
    references: [users.id],
  }),

  files: many(files),
}));
