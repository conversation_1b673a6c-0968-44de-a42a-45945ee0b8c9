import { relations } from 'drizzle-orm';
import {
  pgTable,
  serial,
  varchar,
  timestamp,
  integer,
} from 'drizzle-orm/pg-core';
import { pgEnum } from 'drizzle-orm/pg-core';
import { transactions } from './transaction';

export const exCroStatusEnum = pgEnum('status', [
  'funds_recieved',
  'completed',
  'disputed',
  'cancelled',
  'expired',
  'funds_released',
]);
export const escrow = pgTable('escrow', {
  id: serial('id').primaryKey(),
  amount: varchar('amount', { length: 255 }).notNull(),
  status: exCroStatusEnum('status').notNull(),
  fee: varchar('fee', { length: 255 }).notNull(),
  deadLine: varchar('dead_line', { length: 255 }).notNull(),
  comments: varchar('description', { length: 255 }),
  transactionId: integer('transaction_id').references(() => transactions.id),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export type Escrow = typeof escrow.$inferSelect;
export type NewEscrow = typeof escrow.$inferInsert;

export const escrowRelations = relations(escrow, ({ one }) => ({
  transaction: one(transactions, {
    fields: [escrow.transactionId],
    references: [transactions.id],
  }),
}));
