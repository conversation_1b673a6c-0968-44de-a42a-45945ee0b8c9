import {
  pgTable,
  serial,
  varchar,
  timestamp,
  boolean,
  pgEnum,
  json,
} from 'drizzle-orm/pg-core';
import { integer } from 'drizzle-orm/pg-core';
import { users } from './users';
import { relations } from 'drizzle-orm';

export const notificationStatusEnum = pgEnum('type', [
  'personal',
  'broadcast',
  'admin',
]);
export const notifications = pgTable('notifications', {
  id: serial('id').primaryKey(),
  title: varchar('title', { length: 255 }).notNull(),
  type: notificationStatusEnum('type').notNull(),
  message: varchar('message', { length: 255 }).notNull(),
  userId: integer('user_id').references(() => users.id),
  metadata: json('metadata'),
  priority: varchar('priority', { length: 50 }).default('normal'),
  category: varchar('category', { length: 100 }),
  isRead: boolean('is_read').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export type Notification = typeof notifications.$inferSelect;
export type NewNotification = typeof notifications.$inferInsert;

export const notificationRelations = relations(notifications, ({ one }) => ({
  user: one(users, { fields: [notifications.userId], references: [users.id] }),
}));
