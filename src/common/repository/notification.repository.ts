import { Inject, Injectable } from '@nestjs/common';
import { INotificationRepository } from 'src/common/interfaces';
import { NewNotification, Notification } from 'src/common/schemas/notification';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import * as schema from '../schemas/notification';
import { count, eq } from 'drizzle-orm';
import { PaginatedResponse, PaginationQuery } from '../interfaces/paginaton';

@Injectable()
export class NotificationRepository implements INotificationRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async markAsRead(id: number): Promise<Notification | null> {
    const result = await this.db
      .update(schema.notifications)
      .set({
        isRead: true,
        updatedAt: new Date(),
      })
      .where(eq(schema.notifications.id, id))
      .returning();

    return result[0] || null;
  }

  async markAsReadAll(userId: number): Promise<boolean> {
    const result = await this.db
      .update(schema.notifications)
      .set({
        isRead: true,
        updatedAt: new Date(),
      })
      .where(eq(schema.notifications.userId, userId))
      .returning();

    return result.length > 0;
  }

  async findById(id: number): Promise<Notification | null> {
    const result = await this.db
      .select()
      .from(schema.notifications)
      .where(eq(schema.notifications.id, id))
      .limit(1);

    return result[0] || null;
  }

  async deleteAll(userId: number): Promise<boolean> {
    const result = await this.db
      .delete(schema.notifications)
      .where(eq(schema.notifications.userId, userId))
      .returning();

    return result.length > 0;
  }

  async create(notificationData: NewNotification): Promise<Notification> {
    const result = await this.db
      .insert(schema.notifications)
      .values({
        ...notificationData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    notificationData: Partial<Notification>,
  ): Promise<Notification | null> {
    const result = await this.db
      .update(schema.notifications)
      .set({
        ...notificationData,
        updatedAt: new Date(),
      })
      .where(eq(schema.notifications.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(schema.notifications)
      .where(eq(schema.notifications.id, id))
      .returning();

    return result.length > 0;
  }

  async findByUserId(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Notification> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(schema.notifications);

    const result = await this.db
      .select()
      .from(schema.notifications)
      .where(eq(schema.notifications.userId, userId))
      .limit(limit)
      .offset(offset)
      .orderBy(schema.notifications.createdAt);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }
}
