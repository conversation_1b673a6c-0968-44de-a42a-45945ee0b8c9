import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { files, File } from '../schemas';
import * as schema from '../schemas/files';
import { IFileRepository } from '../interfaces/file-repository.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class FileRepository implements IFileRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async create(fileData: Partial<File>): Promise<File> {
    const result = await this.db
      .insert(files)
      .values({ ...fileData })
      .returning();

    return result[0];
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(files)
      .where(eq(files.id, id))
      .returning();

    return result.length > 0;
  }

  async findByUserId(userId: number): Promise<File[] | null> {
    const result = await this.db
      .select()
      .from(files)
      .where(eq(files.kycId, userId));

    return result || null;
  }

  async findByTransactionId(transactionId: number): Promise<File[] | null> {
    const result = await this.db
      .select()
      .from(files)
      .where(eq(files.transactionId, transactionId));

    return result || null;
  }
}
