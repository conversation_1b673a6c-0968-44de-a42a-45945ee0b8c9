import { Injectable, Inject } from '@nestjs/common';
import { eq, desc, count } from 'drizzle-orm';
import { paymentMethods, PaymentMethod, NewPaymentMethod } from '../schemas';
import * as schema from '../schemas/payment-method';
import { IPaymentMethodRepository } from '../interfaces/payment-method.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginationQuery, PaginatedResponse } from '../interfaces';

@Injectable()
export class PaymentMethodRepository implements IPaymentMethodRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async findAll(
    query: PaginationQuery,
  ): Promise<PaginatedResponse<PaymentMethod>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(paymentMethods);

    const data = await this.db
      .select()
      .from(paymentMethods)
      .limit(limit)
      .offset(offset)
      .orderBy(desc(paymentMethods.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findById(id: number): Promise<PaymentMethod | null> {
    const result = await this.db
      .select()
      .from(paymentMethods)
      .where(eq(paymentMethods.id, id))
      .limit(1);

    return result[0] || null;
  }

  async create(entity: NewPaymentMethod): Promise<PaymentMethod> {
    const result = await this.db
      .insert(paymentMethods)
      .values({
        ...entity,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    entity: Partial<PaymentMethod>,
  ): Promise<PaymentMethod | null> {
    const result = await this.db
      .update(paymentMethods)
      .set({
        ...entity,
        updatedAt: new Date(),
      })
      .where(eq(paymentMethods.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(paymentMethods)
      .where(eq(paymentMethods.id, id))
      .returning();

    return result.length > 0;
  }

  async createMtnPaymentMethod(
    userId: number,
    data: NewPaymentMethod,
  ): Promise<Partial<PaymentMethod>> {
    const result = await this.db
      .insert(paymentMethods)
      .values({
        ...data,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning({
        id: paymentMethods.id,
        name: paymentMethods.name,
        type: paymentMethods.type,
        currency: paymentMethods.currency,
        provider: paymentMethods.provider,
        idDefault: paymentMethods.idDefault,
        accountNumber: paymentMethods.accountNumber,
        countryCode: paymentMethods.countryCode,
        isVerified: paymentMethods.isVerified,
        accountName: paymentMethods.accountName,
        createdAt: paymentMethods.createdAt,
        updatedAt: paymentMethods.updatedAt,
      });

    return result[0];
  }

  async createOrangePaymentMethod(
    userId: number,
    data: NewPaymentMethod,
  ): Promise<Partial<PaymentMethod>> {
    const result = await this.db
      .insert(paymentMethods)
      .values({
        ...data,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning({
        id: paymentMethods.id,
        name: paymentMethods.name,
        type: paymentMethods.type,
        currency: paymentMethods.currency,
        provider: paymentMethods.provider,
        idDefault: paymentMethods.idDefault,
        accountNumber: paymentMethods.accountNumber,
        countryCode: paymentMethods.countryCode,
        isVerified: paymentMethods.isVerified,
        accountName: paymentMethods.accountName,
        createdAt: paymentMethods.createdAt,
        updatedAt: paymentMethods.updatedAt,
      });

    return result[0];
  }

  async createBankPaymentMethod(
    userId: number,
    data: NewPaymentMethod,
  ): Promise<Partial<PaymentMethod>> {
    const result = await this.db
      .insert(paymentMethods)
      .values({
        ...data,
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning({
        id: paymentMethods.id,
        name: paymentMethods.name,
        type: paymentMethods.type,
        currency: paymentMethods.currency,
        provider: paymentMethods.provider,
        expireDate: paymentMethods.expireDate,
        cvv: paymentMethods.cvv,
        idDefault: paymentMethods.idDefault,
        accountNumber: paymentMethods.accountNumber,
        isVerified: paymentMethods.isVerified,
        accountName: paymentMethods.accountName,
        createdAt: paymentMethods.createdAt,
        updatedAt: paymentMethods.updatedAt,
      });

    return result[0];
  }

  async getAllUserPaymentMethods(
    userId: number,
  ): Promise<PaymentMethod[] | null> {
    const result = await this.db
      .select()
      .from(paymentMethods)
      .where(eq(paymentMethods.userId, userId));

    return result || null;
  }

  async getPaymentMethodById(id: number): Promise<PaymentMethod | null> {
    const result = await this.db
      .select()
      .from(paymentMethods)
      .where(eq(paymentMethods.id, id))
      .limit(1);

    return result[0] || null;
  }
}
