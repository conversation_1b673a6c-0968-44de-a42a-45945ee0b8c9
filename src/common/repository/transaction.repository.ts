import { Injectable, Inject } from '@nestjs/common';
import {
  eq,
  desc,
  count,
  and,
  like,
  or,
  gte,
  lte,
  asc,
  SQL,
  sum,
  avg,
  max,
  min,
  isNotNull,
} from 'drizzle-orm';
import {
  transactions,
  Transaction,
  NewTransaction,
  escrow,
  files,
  users,
} from '../schemas';
import * as schema from '../schemas/transaction';
import { ITransactionRepository } from '../interfaces';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { PaginatedResponse, TransactionFilter } from '../interfaces';

@Injectable()
export class TransactionRepository implements ITransactionRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async findAll(query: TransactionFilter): Promise<PaginatedResponse<any>> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = this.buildWhereConditions(query);

    // Count total items with filters
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    // Build order by clause
    const orderBy = this.buildOrderBy(query);

    const data = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        issuer: users,
        receiver: users,
      })
      .from(transactions)
      .leftJoin(users, eq(transactions.initiatedBy, users.id))
      .leftJoin(users, eq(transactions.receivedBy, users.id))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .where(whereConditions)
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async create(transactionData: NewTransaction): Promise<Transaction> {
    const result = await this.db
      .insert(transactions)
      .values({
        ...transactionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    transactionData: Partial<Transaction>,
  ): Promise<Transaction | null> {
    const result = await this.db
      .update(transactions)
      .set({
        ...transactionData,
        updatedAt: new Date(),
      })
      .where(eq(transactions.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(transactions)
      .where(eq(transactions.id, id))
      .returning();

    return result.length > 0;
  }

  async findById(id: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, id))
      .limit(1);

    return result[0] || null;
  }

  async findByUserId(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    // Build where conditions and add user filter
    const baseConditions = this.buildWhereConditions(query);
    const userCondition = eq(transactions.initiatedBy, userId);
    const whereConditions = baseConditions
      ? and(userCondition, baseConditions)
      : userCondition;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    const orderBy = this.buildOrderBy(query);

    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        issuer: users,
        receiver: users,
      })
      .from(transactions)
      .where(whereConditions)
      .leftJoin(users, eq(transactions.initiatedBy, users.id))
      .leftJoin(users, eq(transactions.receivedBy, users.id))
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .limit(limit)
      .offset(offset)
      .orderBy(orderBy);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByReceivedBy(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;

    // Build where conditions and add user filter
    const baseConditions = this.buildWhereConditions(query);
    const userCondition = eq(transactions.receivedBy, userId);
    const whereConditions = baseConditions
      ? and(userCondition, baseConditions)
      : userCondition;

    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions)
      .where(whereConditions);

    const orderBy = this.buildOrderBy(query);

    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        issuer: users,
        receiver: users,
      })
      .from(transactions)
      .where(whereConditions)
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .leftJoin(users, eq(transactions.initiatedBy, users.id))
      .leftJoin(users, eq(transactions.receivedBy, users.id))
      .limit(limit)
      .offset(offset)

      .orderBy(orderBy);

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  async findByEscrowId(escrowId: number): Promise<Transaction | null> {
    const result = await this.db
      .select()
      .from(transactions)
      .where(eq(transactions.id, escrowId))
      .limit(1);

    return result[0] || null;
  }

  async findByPaymentMethodId(
    paymentMethodId: number,
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null> {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 10, 100);
    const offset = (page - 1) * limit;
    const [{ count: totalItems }] = await this.db
      .select({ count: count() })
      .from(transactions);

    const result = await this.db
      .select({
        transaction: transactions,
        escrow: escrow,
        files: files,
        issuer: users,
        receiver: users,
      })
      .from(transactions)
      .where(
        and(
          eq(transactions.paymentMethodId, paymentMethodId),
          eq(transactions.initiatedBy, userId),
        ),
      )
      .leftJoin(escrow, eq(transactions.id, escrow.transactionId))
      .leftJoin(files, eq(transactions.id, files.transactionId))
      .leftJoin(users, eq(transactions.initiatedBy, users.id))
      .leftJoin(users, eq(transactions.receivedBy, users.id))
      .limit(limit)
      .offset(offset)
      .orderBy(desc(transactions.createdAt));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      data: result,
      meta: {
        currentPage: page,
        itemsPerPage: limit,
        totalItems,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      },
    };
  }

  private buildWhereConditions(
    query: TransactionFilter,
  ): SQL<unknown> | undefined {
    const conditions: SQL<unknown>[] = [];

    if (query.status) conditions.push(eq(transactions.status, query.status));

    if (query.search) {
      conditions.push(
        or(
          like(transactions.title, `%${query.search}%`),
          like(transactions.description, `%${query.search}%`),
        )!,
      );
    }

    return conditions.length > 0 ? and(...conditions) : undefined;
  }

  private buildOrderBy(query: TransactionFilter): SQL<unknown> {
    const sortBy = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder || 'desc';

    const column = {
      createdAt: transactions.createdAt,
      updatedAt: transactions.updatedAt,
      amount: transactions.amount,
      deadLine: transactions.deadLine,
      title: transactions.title,
    }[sortBy];

    return sortOrder === 'asc' ? asc(column) : desc(column);
  }

  async getGeneralStats(): Promise<any> {
    const [stats] = await this.db
      .select({
        totalTransactions: count(),
        totalVolume: sum(transactions.amount),
        averageAmount: avg(transactions.amount),
        maxAmount: max(transactions.amount),
        minAmount: min(transactions.amount),
      })
      .from(transactions);

    const statusStats = await this.db
      .select({
        status: transactions.status,
        count: count(),
        volume: sum(transactions.amount),
      })
      .from(transactions)
      .groupBy(transactions.status);

    const categoryStats = await this.db
      .select({
        category: transactions.category,
        count: count(),
        volume: sum(transactions.amount),
      })
      .from(transactions)
      .where(isNotNull(transactions.category))
      .groupBy(transactions.category);

    return {
      general: stats,
      byStatus: statusStats,
      byCategory: categoryStats,
    };
  }

  async getUserStats(userId: number): Promise<any> {
    // Stats for transactions initiated by user
    const [initiatedStats] = await this.db
      .select({
        count: count(),
        totalAmount: sum(transactions.amount),
        averageAmount: avg(transactions.amount),
        maxAmount: max(transactions.amount),
        minAmount: min(transactions.amount),
      })
      .from(transactions)
      .where(eq(transactions.initiatedBy, userId));

    // Stats for transactions received by user
    const [receivedStats] = await this.db
      .select({
        count: count(),
        totalAmount: sum(transactions.amount),
        averageAmount: avg(transactions.amount),
        maxAmount: max(transactions.amount),
        minAmount: min(transactions.amount),
      })
      .from(transactions)
      .where(eq(transactions.receivedBy, userId));

    // Status breakdown for user's transactions
    const statusStats = await this.db
      .select({
        status: transactions.status,
        count: count(),
        volume: sum(transactions.amount),
      })
      .from(transactions)
      .where(
        or(
          eq(transactions.initiatedBy, userId),
          eq(transactions.receivedBy, userId),
        ),
      )
      .groupBy(transactions.status);

    return {
      initiated: initiatedStats,
      received: receivedStats,
      byStatus: statusStats,
    };
  }

  async getStatsByDateRange(startDate: Date, endDate: Date): Promise<any[]> {
    // This is a simplified version - in a real implementation,
    // you might want to use database-specific date functions
    return await this.db
      .select({
        date: transactions.createdAt,
        count: count(),
        volume: sum(transactions.amount),
        averageAmount: avg(transactions.amount),
      })
      .from(transactions)
      .where(
        and(
          gte(transactions.createdAt, startDate),
          lte(transactions.createdAt, endDate),
        ),
      )
      .groupBy(transactions.createdAt)
      .orderBy(transactions.createdAt);
  }

  async getTopUsers(limit: number = 10): Promise<any[]> {
    return await this.db
      .select({
        userId: transactions.initiatedBy,
        transactionCount: count(),
        totalVolume: sum(transactions.amount),
        averageAmount: avg(transactions.amount),
      })
      .from(transactions)
      .where(isNotNull(transactions.initiatedBy))
      .groupBy(transactions.initiatedBy)
      .orderBy(desc(sum(transactions.amount)))
      .limit(limit);
  }

  async getTransactionTrends(days: number = 30): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return await this.db
      .select({
        date: transactions.createdAt,
        count: count(),
        volume: sum(transactions.amount),
      })
      .from(transactions)
      .where(gte(transactions.createdAt, startDate))
      .groupBy(transactions.createdAt)
      .orderBy(transactions.createdAt);
  }
}
