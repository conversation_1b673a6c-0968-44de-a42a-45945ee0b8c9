/* eslint-disable @typescript-eslint/no-redundant-type-constituents */
import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { escrow, Escrow, NewEscrow, transactions } from '../schemas';
import { IEscrowRepository } from '../interfaces';
import { DATABASE_CONNECTION } from 'src/configs';
import * as schema from '../schemas/escrow';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class EscrowRepository implements IEscrowRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async findByTransactionId(transactionId: number): Promise<any | null> {
    const result = await this.db
      .select({
        escrow,
        transaction: transactions,
      })
      .from(escrow)
      .leftJoin(transactions, eq(escrow.transactionId, transactions.id))
      .where(eq(escrow.transactionId, transactionId))
      .limit(1);

    return result[0] || null;
  }

  async findById(id: number): Promise<any | null> {
    const result = await this.db
      .select({
        escrow,
        transaction: transactions,
      })
      .from(escrow)
      .leftJoin(transactions, eq(escrow.transactionId, transactions.id))
      .where(eq(escrow.id, id))
      .limit(1);

    return result[0] || null;
  }
  async create(escrowData: NewEscrow): Promise<Escrow> {
    const result = await this.db
      .insert(escrow)
      .values({ ...escrowData })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    escrowData: Partial<Escrow>,
  ): Promise<Escrow | null> {
    const result = await this.db
      .update(escrow)
      .set({
        ...escrowData,
      })
      .where(eq(escrow.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(escrow)
      .where(eq(escrow.id, id))
      .returning();

    return result.length > 0;
  }
}
