import { Transaction } from '../schemas';
import { TransactionFilter } from './filters.interface';
import { PaginatedResponse } from './paginaton';
import { IRepository } from './repository.interface';

export interface ITransactionRepository extends IRepository<Transaction> {
  findByUserId(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null>;
  findByReceivedBy(
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null>;
  findByEscrowId(escrowId: number): Promise<Transaction | null>;
  findByPaymentMethodId(
    paymentMethodId: number,
    userId: number,
    query: TransactionFilter,
  ): Promise<PaginatedResponse<any> | null>;

  findById(id: number): Promise<Transaction | null>;
  create(transactionData: Partial<Transaction>): Promise<Transaction>;
  update(
    id: number,
    transactionData: Partial<Transaction>,
  ): Promise<Transaction | null>;
  delete(id: number): Promise<boolean>;
}
