import { Notification, NewNotification } from '../schemas';
import { PaginatedResponse, PaginationQuery } from './paginaton';

export interface INotificationRepository {
  create(notificationData: NewNotification): Promise<Notification>;
  update(
    id: number,
    notificationData: Partial<Notification>,
  ): Promise<Notification | null>;
  delete(id: number): Promise<boolean>;
  findById(id: number): Promise<Notification | null>;
  findByUserId(
    userId: number,
    query: PaginationQuery,
  ): Promise<PaginatedResponse<Notification> | null>;
  markAsRead(id: number): Promise<Notification | null>;
  deleteAll(userId: number): Promise<boolean>;
  markAsReadAll(userId: number): Promise<boolean>;
}
